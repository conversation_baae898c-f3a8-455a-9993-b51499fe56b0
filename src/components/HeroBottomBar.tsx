'use client';

import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

export default function HeroBottomBar() {
  return (
    <div className="w-full h-[100px] bg-gradient-to-r from-purple-900/20 via-blue-900/20 to-pink-900/20 backdrop-blur-sm border-t border-gray-700/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="flex items-center justify-between h-full">
          {/* Description */}
          <div className="flex-1 max-w-2xl">
            <div className="flex items-center space-x-3 mb-2">
              <div className="flex items-center space-x-2">
                <Brain className="w-6 h-6 text-purple-400" />
                <Zap className="w-5 h-5 text-blue-400" />
                <Sparkles className="w-5 h-5 text-pink-400" />
              </div>
              <h3 className="text-lg font-bold text-white">H-AI Platform</h3>
            </div>
            <p className="text-gray-300 text-base md:text-lg leading-relaxed">
              <span className="text-purple-400 font-semibold">AI ecosystem</span> for creating
              <span className="text-blue-400 font-medium"> websites, bots, videos</span>.
              Order from freelancers or get AI solutions instantly.
            </p>
          </div>

          {/* CTA Button */}
          <div className="flex-shrink-0 ml-8">
            <Link
              href="/en/solutions"
              className="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 via-blue-600 to-pink-600 hover:from-purple-700 hover:via-blue-700 hover:to-pink-700 text-white rounded-2xl transition-all duration-300 font-bold text-lg shadow-2xl hover:shadow-purple-500/25 hover:scale-105 border border-white/10"
            >
              <span className="mr-3">View All Solutions</span>
              <div className="relative">
                <ArrowRight className="w-6 h-6 transition-transform duration-300 group-hover:translate-x-1" />
                <div className="absolute inset-0 bg-white/20 rounded-full scale-0 group-hover:scale-150 transition-transform duration-300" />
              </div>
              
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/0 via-white/5 to-purple-400/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 rounded-2xl" />
            </Link>
          </div>
        </div>

        {/* Floating particles effect */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/2 left-1/4 w-2 h-2 bg-purple-400/30 rounded-full animate-pulse" />
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-400/40 rounded-full animate-ping" />
          <div className="absolute bottom-1/3 left-1/2 w-1.5 h-1.5 bg-pink-400/30 rounded-full animate-pulse delay-300" />
        </div>
      </div>
    </div>
  );
}
