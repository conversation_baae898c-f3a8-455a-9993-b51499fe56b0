'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Heart, Eye, Crown, Sparkles } from 'lucide-react';
import { ReelsService, Reel } from '@/lib/appwrite/reels';
import Link from 'next/link';

interface ReelsGridProps {
  limit?: number;
  showTitle?: boolean;
}

export default function ReelsGrid({ limit = 4, showTitle = true }: ReelsGridProps) {
  const [reels, setReels] = useState<Reel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadReels();
  }, [limit]);

  const loadReels = async () => {
    try {
      setLoading(true);
      const topReels = await ReelsService.getTopReels(limit);
      setReels(topReels);
    } catch (error) {
      console.error('Error loading reels:', error);
      // Fallback to mock data for demo
      setReels(getMockReels());
    } finally {
      setLoading(false);
    }
  };

  const getMockReels = (): Reel[] => [
    {
      $id: '1',
      title: 'AI Website Builder',
      description: 'Create stunning websites with AI in minutes',
      videoUrl: '/videos/website-demo.mp4',
      thumbnailUrl: '/images/website-thumb.jpg',
      category: 'website',
      creatorId: 'creator1',
      creatorName: 'Alex Designer',
      isPremium: true,
      views: 15420,
      likes: 892,
      rating: 4.9,
      duration: 45,
      isActive: true,
      tags: ['AI', 'Website', 'Design']
    },
    {
      $id: '2',
      title: 'TikTok Video Creator',
      description: 'Generate viral TikTok content automatically',
      videoUrl: '/videos/tiktok-demo.mp4',
      thumbnailUrl: '/images/tiktok-thumb.jpg',
      category: 'video',
      creatorId: 'creator2',
      creatorName: 'Video Pro',
      isPremium: false,
      views: 23100,
      likes: 1340,
      rating: 4.8,
      duration: 30,
      isActive: true,
      tags: ['Video', 'TikTok', 'Content']
    },
    {
      $id: '3',
      title: 'Telegram Bot Assistant',
      description: 'Smart bot for customer support',
      videoUrl: '/videos/bot-demo.mp4',
      thumbnailUrl: '/images/bot-thumb.jpg',
      category: 'bot',
      creatorId: 'creator3',
      creatorName: 'Bot Master',
      isPremium: false,
      views: 18750,
      likes: 967,
      rating: 4.7,
      duration: 60,
      isActive: true,
      tags: ['Bot', 'AI', 'Automation']
    },
    {
      $id: '4',
      title: 'Logo Design AI',
      description: 'Professional logos in seconds',
      videoUrl: '/videos/logo-demo.mp4',
      thumbnailUrl: '/images/logo-thumb.jpg',
      category: 'design',
      creatorId: 'creator4',
      creatorName: 'Design Guru',
      isPremium: false,
      views: 12890,
      likes: 743,
      rating: 4.6,
      duration: 25,
      isActive: true,
      tags: ['Design', 'Logo', 'Branding']
    }
  ];

  const handleReelClick = async (reel: Reel) => {
    try {
      await ReelsService.incrementViews(reel.$id!);
      // Update local state
      setReels(prev => prev.map(r => 
        r.$id === reel.$id ? { ...r, views: r.views + 1 } : r
      ));
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (loading) {
    return (
      <div className="w-full">
        {showTitle && (
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-8 text-center">
            🔥 Trending AI Solutions
          </h2>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {Array.from({ length: limit }).map((_, index) => (
            <div key={index} className="aspect-[9/16] bg-gray-800/50 rounded-2xl animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {showTitle && (
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-8 text-center">
          🔥 Trending AI Solutions
        </h2>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {reels.map((reel, index) => (
          <Link
            key={reel.$id}
            href={`/en/solutions/${reel.$id}`}
            onClick={() => handleReelClick(reel)}
            className="group relative aspect-[9/16] bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl overflow-hidden border border-gray-700/50 hover:border-purple-500/50 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/20"
          >
            {/* Background Video/Image */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent">
              {reel.thumbnailUrl ? (
                <img
                  src={reel.thumbnailUrl}
                  alt={reel.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-purple-600/20 to-blue-600/20 flex items-center justify-center">
                  <Sparkles className="w-16 h-16 text-purple-400" />
                </div>
              )}
            </div>

            {/* Premium Badge */}
            {reel.isPremium && (
              <div className="absolute top-3 right-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
                <Crown className="w-3 h-3" />
                <span>PRO</span>
              </div>
            )}

            {/* Play Button */}
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                <Play className="w-8 h-8 text-white ml-1" />
              </div>
            </div>

            {/* Content */}
            <div className="absolute bottom-0 left-0 right-0 p-4">
              <div className="space-y-2">
                {/* Category Badge */}
                <div className="inline-block">
                  <span className="px-2 py-1 bg-purple-600/80 text-white text-xs rounded-full font-medium capitalize">
                    {reel.category}
                  </span>
                </div>

                {/* Title */}
                <h3 className="text-white font-bold text-sm leading-tight line-clamp-2">
                  {reel.title}
                </h3>

                {/* Creator */}
                <p className="text-gray-300 text-xs">
                  by {reel.creatorName}
                </p>

                {/* Stats */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-xs text-gray-300">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{formatNumber(reel.views)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Heart className="w-3 h-3" />
                        <span>{formatNumber(reel.likes)}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-400">★</span>
                      <span>{reel.rating}</span>
                    </div>
                  </div>

                  {/* Additional Stats for this Reel */}
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-gray-800/50 rounded-lg p-2 text-center">
                      <div className="text-white font-semibold">{Math.floor(reel.views * 0.15)}</div>
                      <div className="text-gray-400">Заказов</div>
                    </div>
                    <div className="bg-gray-800/50 rounded-lg p-2 text-center">
                      <div className="text-green-400 font-semibold">${Math.floor(reel.views * 0.02)}</div>
                      <div className="text-gray-400">Доход</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-1 text-xs">
                    <div className="bg-blue-600/20 rounded-lg p-2 text-center">
                      <div className="text-blue-400 font-semibold">{Math.floor(reel.views * 0.08)}</div>
                      <div className="text-gray-400">AI</div>
                    </div>
                    <div className="bg-purple-600/20 rounded-lg p-2 text-center">
                      <div className="text-purple-400 font-semibold">{Math.floor(reel.views * 0.05)}</div>
                      <div className="text-gray-400">Фрил.</div>
                    </div>
                    <div className="bg-green-600/20 rounded-lg p-2 text-center">
                      <div className="text-green-400 font-semibold">{Math.floor(reel.views * 0.02)}</div>
                      <div className="text-gray-400">Проект.</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Hover Gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-purple-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </Link>
        ))}
      </div>
    </div>
  );
}
